import { gql } from '@apollo/client';

export const GET_CONTRACT = gql`
  query GetContract($id: ID!) {
    getContract(id: $id) {
      id
      jobId
      proposalId
      clientId
      freelancerId
      title
      description
      type
      status
      terms
      startDate
      endDate
      budget
      paymentSchedule {
        id
        amount
        dueDate
        status
        description
      }
      deliverables {
        id
        title
        description
        dueDate
        status
        attachments
      }
      createdAt
      updatedAt
    }
  }
`;

export const LIST_CONTRACTS = gql`
  query ListContracts(
    $filter: ModelContractFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listContracts(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        jobId
        clientId
        freelancerId
        status
        createdAt
        updatedAt
      }
      nextToken
    }
  }
`;

export const GET_CONTRACTS_BY_JOB = gql`
  query GetContractsByJob($jobId: ID!) {
    getContractsByJob(jobId: $jobId) {
      items {
        id
        title
        status
        type
        startDate
        endDate
        budget
        createdAt
      }
    }
  }
`;

export const GET_USER_CONTRACTS = gql`
  query GetUserContracts($userId: ID!, $status: ContractStatus) {
    getUserContracts(userId: $userId, status: $status) {
      items {
        id
        jobId
        title
        status
        type
        startDate
        endDate
        budget
        createdAt
      }
    }
  }
`;
