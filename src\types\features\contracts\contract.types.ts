import { Job } from '../jobs/job.types';
import { Proposal } from '../proposals/proposal.types';

export enum ContractStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  DISPUTED = 'DISPUTED'
}

export enum ContractType {
  FIXED_PRICE = 'FIXED_PRICE',
  HOURLY = 'HOURLY'
}

export interface Contract {
  id: string;
  jobId: string;
  job?: Job;
  proposalId: string;
  proposal?: Proposal;
  clientId: string;
  freelancerId: string;
  title: string;
  description: string;
  type: ContractType;
  status: ContractStatus;
  terms: string;
  startDate: string;
  endDate?: string;
  budget: number;
  paymentSchedule: PaymentSchedule[];
  deliverables: Deliverable[];
  createdAt: string;
  updatedAt: string;
}

export interface PaymentSchedule {
  id: string;
  amount: number;
  dueDate: string;
  status: 'PENDING' | 'PAID' | 'OVERDUE';
  description: string;
}

export interface Deliverable {
  id: string;
  title: string;
  description: string;
  dueDate: string;
  status: 'PENDING' | 'IN_REVIEW' | 'APPROVED' | 'REJECTED';
  attachments?: string[];
}

export interface CreateContractDto {
  jobId: string;
  proposalId: string;
  title: string;
  description: string;
  type: ContractType;
  terms: string;
  startDate: string;
  endDate?: string;
  budget: number;
  paymentSchedule: Omit<PaymentSchedule, 'id' | 'status'>[];
  deliverables: Omit<Deliverable, 'id' | 'status'>[];
}

export interface UpdateContractDto {
  status?: ContractStatus;
  endDate?: string;
  terms?: string;
  paymentSchedule?: PaymentSchedule[];
  deliverables?: Deliverable[];
}

export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ContractResponse extends ApiResponse<Contract> {}
export interface ContractsResponse extends PaginatedResponse<Contract> {}

export interface ContractCardProps {
  contract: Contract;
  onView?: (contract: Contract) => void;
  onUpdateStatus?: (contractId: string, status: ContractStatus) => void;
  className?: string;
}

export interface ContractFilters {
  status?: ContractStatus;
  type?: ContractType;
  clientId?: string;
  freelancerId?: string;
  jobId?: string;
  proposalId?: string;
  startDateFrom?: string;
  startDateTo?: string;
  endDateFrom?: string;
  endDateTo?: string;
}

export interface ContractSearchParams {
  status?: ContractStatus;
  type?: ContractType;
  clientId?: string;
  freelancerId?: string;
  startDateFrom?: string;
  startDateTo?: string;
  endDateFrom?: string;
  endDateTo?: string;
  page?: number;
  limit?: number;
  sortBy?: 'startDate' | 'endDate' | 'createdAt' | 'budget';
  sortOrder?: 'asc' | 'desc';
}
