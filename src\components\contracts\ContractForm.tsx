"use client";

import React, { useState } from "react";
import { useForm, use<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { Plus, Trash2 } from "lucide-react";

import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { Textarea } from "@/components/ui/Textarea";
import { DatePicker } from "@/components/ui/DatePicker";
import { Form, FormField } from "@/components/ui/Form";
import { Select } from "@/components/ui/Select";
import {
  Contract,
  ContractType,
  CreateContractDto,
} from "@/types/features/contracts/contract.types";

const paymentScheduleSchema = yup.object().shape({
  amount: yup
    .number()
    .min(0.01, "Amount must be greater than 0")
    .required("Amount is required"),
  dueDate: yup.date().required("Due date is required"),
  description: yup.string().required("Description is required"),
});

const deliverableSchema = yup.object().shape({
  title: yup.string().required("Title is required"),
  description: yup.string().optional(),
  dueDate: yup.date().required("Due date is required"),
});

const contractFormSchema = yup.object().shape({
  title: yup.string().required("Title is required"),
  description: yup.string().required("Description is required"),
  type: yup.string().required("Type is required"),
  terms: yup.string().required("Terms are required"),
  startDate: yup.date().required("Start date is required"),
  endDate: yup
    .date()
    .optional()
    .when("startDate", (startDate: Date, schema) => {
      return startDate
        ? schema.min(startDate, "End date must be after start date")
        : schema;
    }),
  budget: yup
    .number()
    .min(0.01, "Budget must be greater than 0")
    .required("Budget is required"),
  paymentSchedule: yup
    .array()
    .of(paymentScheduleSchema)
    .min(1, "At least one payment milestone is required"),
  deliverables: yup.array().of(deliverableSchema).optional(),
  clientId: yup.string().required("Client is required"),
  freelancerId: yup.string().required("Freelancer is required"),
  jobId: yup.string().optional(),
  proposalId: yup.string().optional(),
});

type ContractFormValues = yup.InferType<typeof contractFormSchema>;

interface ContractFormProps {
  initialData?: Partial<Contract> & { jobId?: string; proposalId?: string };
  onSubmit: (data: CreateContractDto) => Promise<void>;
  isLoading?: boolean;
  submitButtonText?: string;
  className?: string;
}

export const ContractForm: React.FC<ContractFormProps> = ({
  initialData,
  onSubmit,
  isLoading = false,
  submitButtonText = "Create Contract",
  className = "",
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ContractFormValues>({
    resolver: yupResolver(contractFormSchema),
    defaultValues: {
      title: initialData?.title || "",
      description: initialData?.description || "",
      type: initialData?.type || ContractType.FIXED_PRICE,
      terms: initialData?.terms || "",
      startDate: initialData?.startDate
        ? new Date(initialData.startDate)
        : new Date(),
      endDate: initialData?.endDate ? new Date(initialData.endDate) : undefined,
      budget: initialData?.budget || 0,
      paymentSchedule: initialData?.paymentSchedule?.length
        ? initialData.paymentSchedule.map((ps) => ({
            ...ps,
            dueDate: new Date(ps.dueDate),
          }))
        : [
            {
              amount: initialData?.budget || 0,
              dueDate: initialData?.endDate
                ? new Date(initialData.endDate)
                : new Date(),
              description: "Final payment",
            },
          ],
      deliverables: initialData?.deliverables?.length
        ? initialData.deliverables.map((d) => ({
            ...d,
            dueDate: new Date(d.dueDate),
          }))
        : [],
    },
  });

  const { register, control, formState: { errors } } = form;

  const {
    fields: paymentFields,
    append: appendPayment,
    remove: removePayment,
  } = useFieldArray({
    control: form.control,
    name: "paymentSchedule",
  });

  const {
    fields: deliverableFields,
    append: appendDeliverable,
    remove: removeDeliverable,
  } = useFieldArray({
    control: form.control,
    name: "deliverables",
  });

  const startDate = form.watch("startDate");

  const handleSubmit = async (data: ContractFormValues) => {
    try {
      setIsSubmitting(true);

      const contractData: CreateContractDto = {
        jobId: initialData?.jobId || "",
        proposalId: initialData?.proposalId || "",
        title: data.title,
        description: data.description,
        type: data.type,
        terms: data.terms,
        startDate: data.startDate.toISOString(),
        endDate: data.endDate?.toISOString(),
        budget: data.budget,
        paymentSchedule: data.paymentSchedule.map((ps) => ({
          amount: ps.amount,
          dueDate: ps.dueDate.toISOString(),
          description: ps.description,
        })),
        deliverables:
          data.deliverables?.map((d) => ({
            title: d.title,
            description: d.description || "",
            dueDate: d.dueDate.toISOString(),
          })) || [],
      };

      await onSubmit(contractData);
    } catch (error) {
      console.error("Error submitting contract:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const addPaymentMilestone = () => {
    appendPayment({
      amount: 0,
      dueDate: new Date(),
      description: "",
    });
  };

  const addDeliverable = () => {
    appendDeliverable({
      title: "",
      description: "",
      dueDate: startDate || new Date(),
    });
  };

  return (
    <Form onSubmit={form.handleSubmit(handleSubmit)}>
      <div className={`space-y-6 ${className}`}>
        <div className="space-y-4">
          <FormField
            label="Contract Title"
            required
            error={errors.title?.message as string}
          >
            <Input
              placeholder="e.g., Website Development Contract"
              {...register('title')}
            />
          </FormField>

          <FormField
            label="Description"
            required
            error={errors.description?.message as string}
          >
            <Textarea
              placeholder="Describe the work to be performed..."
              className="min-h-[120px]"
              {...register('description')}
            />
          </FormField>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField label="Contract Type" required error={errors.type?.message as string}>
              <Select
                {...register('type')}
                options={[
                  { value: ContractType.FIXED_PRICE, label: 'Fixed Price' },
                  { value: ContractType.HOURLY, label: 'Hourly' },
                ]}
                placeholder="Select contract type"
                className="w-full"
              />
            </FormField>

            <FormField label="Total Budget ($)" required error={errors.budget?.message as string}>
              <Input
                type="number"
                step="0.01"
                min="0.01"
                placeholder="0.00"
                {...register('budget', { valueAsNumber: true })}
              />
            </FormField>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField label="Start Date" required error={errors.startDate?.message as string}>
              <Controller
                name="startDate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select start date"
                    minDate={new Date()}
                  />
                )}
              />
            </FormField>

            <FormField label="End Date (Optional)" error={errors.endDate?.message as string}>
              <Controller
                name="endDate"
                control={control}
                render={({ field }) => (
                  <DatePicker
                    value={field.value || undefined}
                    onChange={field.onChange}
                    placeholder="Select end date (optional)"
                    minDate={startDate}
                  />
                )}
              />
            </FormField>
          </div>

          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="font-medium">Payment Schedule</h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addPaymentMilestone}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Milestone
              </Button>
            </div>

            {paymentFields.length === 0 ? (
              <div className="text-sm text-muted-foreground text-center py-4 border rounded">
                No payment milestones added yet
              </div>
            ) : (
              <div className="space-y-4">
                {paymentFields.map((field, index) => (
                  <div
                    key={field.id}
                    className="grid grid-cols-1 md:grid-cols-12 gap-4 items-start p-4 border rounded"
                  >
                    <div className="md:col-span-4">
                      <FormField
                        label={index > 0 ? undefined : 'Amount'}
                        error={errors?.paymentSchedule?.[index]?.amount?.message as string}
                      >
                        <div className="relative">
                          <span className="absolute left-3 top-2.5 text-muted-foreground">$</span>
                          <Input
                            type="number"
                            step="0.01"
                            min="0.01"
                            placeholder="0.00"
                            className="pl-8"
                            {...register(`paymentSchedule.${index}.amount`, { valueAsNumber: true })}
                          />
                        </div>
                      </FormField>
                    </div>

                    <div className="md:col-span-4">
                      <FormField
                        label={index > 0 ? undefined : 'Due Date'}
                        error={errors?.paymentSchedule?.[index]?.dueDate?.message as string}
                      >
                        <Controller
                          name={`paymentSchedule.${index}.dueDate`}
                          control={control}
                          render={({ field }) => (
                            <DatePicker
                              value={field.value}
                              onChange={field.onChange}
                              placeholder="Select due date"
                              minDate={startDate}
                              showTimeSelect={false}
                            />
                          )}
                        />
                      </FormField>
                    </div>

                    <div className="md:col-span-3">
                      <FormField
                        label={index > 0 ? undefined : 'Description'}
                        error={errors?.paymentSchedule?.[index]?.description?.message as string}
                      >
                        <Input
                          placeholder="e.g., Initial deposit"
                          {...register(`paymentSchedule.${index}.description`)}
                        />
                      </FormField>
                    </div>

                    <div className="flex items-center justify-end md:col-span-1">
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={() => removePayment(index)}
                        disabled={paymentFields.length <= 1}
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="font-medium">Deliverables (Optional)</h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addDeliverable}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Deliverable
              </Button>
            </div>

            {deliverableFields.length === 0 ? (
              <div className="text-sm text-muted-foreground text-center py-4 border rounded">
                No deliverables added yet
              </div>
            ) : (
              <div className="space-y-4">
                {deliverableFields.map((field, index) => (
                  <div key={field.id} className="space-y-4 p-4 border rounded">
                    <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
                      <div className="md:col-span-5">
                        <FormField
                          label={index > 0 ? undefined : 'Title'}
                          error={errors?.deliverables?.[index]?.title?.message as string}
                        >
                          <Input
                            placeholder="e.g., Homepage Design"
                            {...register(`deliverables.${index}.title`)}
                          />
                        </FormField>
                      </div>

                      <div className="md:col-span-5">
                        <FormField label="Due Date" error={errors?.deliverables?.[index]?.dueDate?.message as string}>
                          <Controller
                            name={`deliverables.${index}.dueDate`}
                            control={control}
                            render={({ field }) => (
                              <DatePicker
                                value={field.value || null}
                                onChange={field.onChange}
                                placeholder="Select due date"
                                minDate={new Date()}
                                className="w-full"
                              />
                            )}
                          />
                        </FormField>
                      </div>

                      <div className="flex items-start justify-end md:col-span-2">
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() => removeDeliverable(index)}
                        >
                          <Trash2 className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                    </div>

                    <FormField
                      label={index > 0 ? undefined : 'Description (Optional)'}
                      error={errors?.deliverables?.[index]?.description?.message as string}
                    >
                      <Textarea
                        placeholder="Describe the deliverable in detail..."
                        className="min-h-[80px]"
                        {...register(`deliverables.${index}.description`)}
                      />
                    </FormField>
                  </div>
                ))}
              </div>
            )}
          </div>

          <FormField
            label="Terms & Conditions"
            error={errors.terms?.message as string}
          >
            <Textarea
              placeholder="Specify the terms and conditions of this contract..."
              className="min-h-[150px] font-mono text-sm"
              {...register('terms')}
            />
            <p className="text-sm text-muted-foreground mt-2">
              Be clear about payment terms, revision policies, ownership
              rights, and any other important conditions.
            </p>
          </FormField>
        </div>
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            disabled={isSubmitting || isLoading}
          >
            Save Draft
          </Button>
          <Button type="submit" disabled={isSubmitting || isLoading}>
            {isSubmitting || isLoading ? "Saving..." : submitButtonText}
          </Button>
        </div>
      </div>
    </Form>
  );
};

export default ContractForm;
