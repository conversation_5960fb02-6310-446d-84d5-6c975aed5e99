"use client";

import React, { useCallback, useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/AuthContext";
import { ContractDetails, ContractStatusUpdate } from "@/components/contracts";
import contractService from "@/api/contracts/contract.service";
import { Button } from "@/components/ui/Button";
import Link from "next/link";
import {
  Contract,
  ContractStatus,
} from "@/types/features/contracts/contract.types";
import { UserProfile } from "@/types/features/auth/auth.types";
import { Icon } from "@/components/ui/Icon";
import { DashboardLayout } from "@/components/layouts/DashboardLayout";

const ContractPage = () => {
  const { id } = useParams() as { id?: string };
  const router = useRouter();
  const { isAuthenticated, user, loading: authLoading } = useAuth() as {
    isAuthenticated: boolean;
    user: UserProfile | null;
    loading: boolean;
  };

  const [contract, setContract] = useState<Contract | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchContract = useCallback(async () => {
    if (!id) return;
    try {
      setIsLoading(true);
      setError(null);
      const c = await contractService.getContract(id);
      setContract(c);
    } catch (err) {
      console.error("Error loading contract:", err);
      setError("Failed to load contract");
    } finally {
      setIsLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchContract();
  }, [fetchContract]);

  const handleStatusUpdate = async (status: ContractStatus) => {
    if (!contract) return;
    try {
      switch (status) {
        case ContractStatus.ACTIVE:
          await contractService.acceptContract(contract.id);
          break;
        case ContractStatus.COMPLETED:
          await contractService.completeContract(contract.id);
          break;
        case ContractStatus.DISPUTED:
          console.log("Dispute raised for contract:", contract.id);
          break;
        default:
          await contractService.updateContractStatus(contract.id, status);
      }

      // Refresh contract after status change
      await fetchContract();
      router.push(`/contracts/${contract.id}`);
    } catch (err) {
      console.error(`Error updating contract status to ${status}:`, err);
      setError("Failed to update status");
    }
  };

  const handleDownloadContract = async () => {
    if (!contract) return { success: false };
    try {
      console.log("Downloading contract:", contract.id);
      // placeholder: actual download implementation goes here
      return { success: true };
    } catch (err) {
      console.error("Error downloading contract:", err);
      setError("Failed to download contract");
      return { success: false };
    }
  };

  const handleSendMessage = () => {
    if (!contract || !user?.id) return;
    const isCurrentUserClient = contract.clientId === user.id;
    const otherPartyId = isCurrentUserClient ? contract.freelancerId : contract.clientId;
    if (otherPartyId) {
      window.open(`/messages/${otherPartyId}`, "_blank");
    }
  };

  if (authLoading || !isAuthenticated || isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full">
          {error && <div className="text-red-500 mb-4">{error}</div>}
          {isLoading ? (
            <div>Loading contract...</div>
          ) : !isAuthenticated ? (
            <div>Please log in to view this contract</div>
          ) : (
            <div>Loading contract...</div>
          )}
          <Icon name="Loader2" size="xl" className="animate-spin text-primary" />
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full px-4">
          <div className="w-full max-w-md p-6 text-center bg-card rounded-lg shadow-sm border">
            <div className="flex items-center justify-center w-12 h-12 mx-auto rounded-full bg-red-100">
              <Icon name="XCircle" size="lg" className="text-red-600" />
            </div>
            <h3 className="mt-3 text-lg font-medium text-foreground">Error</h3>
            <p className="mt-2 text-sm text-muted-foreground">{error}</p>
            <div className="mt-6">
              <button
                type="button"
                onClick={() => router.push("/contracts")}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                Back
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!contract) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full px-4">
          <div className="w-full max-w-md p-6 text-center bg-card rounded-lg shadow-sm border">
            <div className="flex items-center justify-center w-12 h-12 mx-auto rounded-full bg-blue-100">
              <Icon name="Info" size="lg" className="text-blue-600" />
            </div>
            <h3 className="mt-3 text-lg font-medium text-foreground">Contract Not Found</h3>
            <p className="mt-2 text-sm text-muted-foreground">The contract you&apos;re looking for doesn&apos;t exist or has been removed.</p>
            <div className="mt-6">
              <button
                type="button"
                onClick={() => router.push("/contracts")}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                Back
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const isClient = user?.id ? contract.clientId === user.id : false;
  const isFreelancer = user?.id ? contract.freelancerId === user.id : false;

  if (!isClient && !isFreelancer) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-full px-4">
          <div className="w-full max-w-md p-6 text-center bg-card rounded-lg shadow-sm border">
            <div className="flex items-center justify-center w-12 h-12 mx-auto rounded-full bg-red-100">
              <Icon name="XCircle" size="lg" className="text-red-600" />
            </div>
            <h3 className="mt-3 text-lg font-medium text-foreground">Not Authorized</h3>
            <p className="mt-2 text-sm text-muted-foreground">You do not have permission to view this contract.</p>
            <div className="mt-6">
              <button
                type="button"
                onClick={() => router.push("/")}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                Back
              </button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="w-full max-w-6xl mx-auto p-4 sm:p-6">
        <div className="flex flex-col gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">{contract.title || "Contract Details"}</h1>
            <p className="text-muted-foreground">Contract ID: {contract.id}</p>
          </div>

          <div className="flex flex-wrap gap-3 items-center">
            <ContractStatusUpdate
              currentStatus={contract.status}
              onStatusUpdate={handleStatusUpdate}
              isClient={isClient}
              isFreelancer={isFreelancer}
            />

            <Button variant="outline" onClick={handleSendMessage}>
              Message {isClient ? "Freelancer" : "Client"}
            </Button>

            <Button variant="outline" onClick={handleDownloadContract}>
              Download Contract
            </Button>

            <Button asChild variant="ghost">
              <Link href="/contracts">Back to Contracts</Link>
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <ContractDetails
              contract={contract}
              isClient={isClient}
              isFreelancer={isFreelancer}
              onDownload={handleDownloadContract}
            />
          </div>

          <div className="space-y-6">
            <div className="bg-card p-6 rounded-lg border">
              <h3 className="font-medium mb-4">Contract Actions</h3>
              <div className="space-y-3">
                <ContractStatusUpdate
                  currentStatus={contract.status}
                  onStatusUpdate={handleStatusUpdate}
                  isClient={isClient}
                  isFreelancer={isFreelancer}
                  className="w-full"
                />

                <Button variant="outline" className="w-full" onClick={handleSendMessage}>
                  Message {isClient ? "Freelancer" : "Client"}
                </Button>

                <div className="w-full">
                  <Button type="button" variant="outline" className="w-full" onClick={handleDownloadContract}>
                    Download Contract
                  </Button>
                </div>
              </div>
            </div>

            <div className="bg-card p-6 rounded-lg border">
              <h3 className="font-medium mb-4">Contract Timeline</h3>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="flex flex-col items-center">
                    <div className="h-3 w-3 rounded-full bg-primary mt-1" />
                    <div className="w-px bg-border h-full my-1" />
                  </div>
                  <div>
                    <p className="font-medium">Created</p>
                    <p className="text-sm text-muted-foreground">{new Date(contract.createdAt).toLocaleDateString()}</p>
                  </div>
                </div>

                {contract.updatedAt !== contract.createdAt && (
                  <div className="flex items-start gap-3">
                    <div className="flex flex-col items-center">
                      <div className="h-3 w-3 rounded-full bg-primary mt-1" />
                      <div className="w-px bg-border h-full my-1" />
                    </div>
                    <div>
                      <p className="font-medium">Last Updated</p>
                      <p className="text-sm text-muted-foreground">{new Date(contract.updatedAt).toLocaleDateString()}</p>
                    </div>
                  </div>
                )}

                {contract.updatedAt && contract.status === ContractStatus.ACTIVE && (
                  <div className="flex items-start gap-3">
                    <div className="flex flex-col items-center">
                      <div className="h-3 w-3 rounded-full bg-green-500 mt-1" />
                      <div className="w-px bg-border h-full my-1" />
                    </div>
                    <div>
                      <p className="font-medium">Activated</p>
                      <p className="text-sm text-muted-foreground">{new Date(contract.updatedAt).toLocaleDateString()}</p>
                    </div>
                  </div>
                )}

                {contract.status === ContractStatus.COMPLETED && (
                  <div className="flex items-start gap-3">
                    <div className="h-3 w-3 rounded-full bg-green-500 mt-1" />
                    <div>
                      <p className="font-medium">Completed</p>
                      <p className="text-sm text-muted-foreground">{new Date(contract.updatedAt || contract.createdAt).toLocaleDateString()}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ContractPage;
