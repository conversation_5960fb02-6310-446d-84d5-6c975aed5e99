import React from 'react';
import { Badge, BadgeProps } from '@/components/ui/Badge';
import { ContractStatus } from '@/types/features/contracts/contract.types';
import { cn } from '@/lib/utils';

const statusVariants: Record<ContractStatus, string> = {
  DRAFT: 'bg-gray-100 text-gray-800 hover:bg-gray-100',
  ACTIVE: 'bg-blue-100 text-blue-800 hover:bg-blue-100',
  COMPLETED: 'bg-green-100 text-green-800 hover:bg-green-100',
  CANCELLED: 'bg-red-100 text-red-800 hover:bg-red-100',
  DISPUTED: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100',
};

const statusLabels: Record<ContractStatus, string> = {
  DRAFT: 'Draft',
  ACTIVE: 'Active',
  COMPLETED: 'Completed',
  CANCELLED: 'Cancelled',
  DISPUTED: 'In Dispute',
};

interface ContractStatusBadgeProps extends Omit<BadgeProps, 'variant'> {
  status: ContractStatus;
  className?: string;
}

export const ContractStatusBadge: React.FC<ContractStatusBadgeProps> = ({
  status,
  className = '',
  ...props
}) => {
  return (
    <Badge
      className={cn(
        'whitespace-nowrap',
        statusVariants[status],
        className
      )}
      {...props}
    >
      {statusLabels[status]}
    </Badge>
  );
};

export default ContractStatusBadge;
