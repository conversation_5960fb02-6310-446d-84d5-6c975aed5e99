import React from 'react';
import { Button } from '@/components/ui/Button';
import { ConfirmDialog } from '@/components/ui/ConfirmDialog';
import { ContractStatus } from '@/types/features/contracts/contract.types';
import { ContractStatusBadge } from './ContractStatusBadge';
import { Loader2 } from 'lucide-react';

interface ContractStatusUpdateProps {
  currentStatus: ContractStatus;
  onStatusUpdate: (status: ContractStatus) => Promise<void>;
  isUpdating?: boolean;
  isClient?: boolean;
  isFreelancer?: boolean;
  className?: string;
}

export const ContractStatusUpdate: React.FC<ContractStatusUpdateProps> = ({
  currentStatus,
  onStatusUpdate,
  isUpdating = false,
  className = '',
}) => {
  const [open, setOpen] = React.useState(false);
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [confirmDialog, setConfirmDialog] = React.useState<{
    open: boolean;
    status: ContractStatus | null;
    statusInfo: { label: string; description: string } | null;
  }>({
    open: false,
    status: null,
    statusInfo: null,
  });

  const getAvailableStatuses = () => {
    switch (currentStatus) {
      case 'DRAFT':
        return [
          { value: 'ACTIVE', label: 'Activate Contract', description: 'Activate this contract to begin work.' },
          { value: 'CANCELLED', label: 'Cancel Contract', description: 'Cancel this contract before it begins.' },
        ];
      case 'ACTIVE':
        return [
          { value: 'COMPLETED', label: 'Mark as Completed', description: 'Mark this contract as completed.' },
          { value: 'DISPUTED', label: 'Raise Dispute', description: 'Raise a dispute for this contract.' },
          { value: 'CANCELLED', label: 'Cancel Contract', description: 'Cancel this contract before completion.' },
        ];
      case 'COMPLETED':
        return [
          { value: 'ACTIVE', label: 'Reopen Contract', description: 'Reopen this completed contract.' },
        ];
      case 'DISPUTED':
        return [
          { value: 'ACTIVE', label: 'Resolve Dispute', description: 'Resolve the dispute and reactivate the contract.' },
          { value: 'CANCELLED', label: 'Cancel Contract', description: 'Cancel this disputed contract.' },
        ];
      case 'CANCELLED':
        return [
          { value: 'DRAFT', label: 'Revert to Draft', description: 'Revert this cancelled contract to draft status.' },
        ];
      default:
        return [];
    }
  };

  const availableStatuses = getAvailableStatuses();

  const handleStatusSelect = (status: ContractStatus) => {
    const statusInfo = getAvailableStatuses().find(s => s.value === status);
    if (statusInfo) {
      setConfirmDialog({
        open: true,
        status,
        statusInfo: {
          label: statusInfo.label,
          description: statusInfo.description,
        },
      });
    }
  };

  const handleConfirm = async () => {
    if (!confirmDialog.status) return;
    
    setIsSubmitting(true);
    try {
      await onStatusUpdate(confirmDialog.status);
      setConfirmDialog(prev => ({ ...prev, open: false }));
      setOpen(false);
    } catch (error) {
      console.error('Failed to update status:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (availableStatuses.length === 0) {
    return null;
  }

  return (
    <>
      <Button 
        variant="outline" 
        className={className} 
        disabled={isUpdating}
        onClick={() => setOpen(true)}
      >
        {isUpdating ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Updating...
          </>
        ) : (
          'Update Status'
        )}
      </Button>

      {open && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-background rounded-lg p-6 w-full max-w-md">
            <h2 className="text-xl font-semibold mb-2">Update Contract Status</h2>
            <p className="text-muted-foreground mb-6">
              Current status: <ContractStatusBadge status={currentStatus} />
            </p>
            
            <div className="space-y-3">
              {availableStatuses.map((status) => (
                <Button
                  key={status.value}
                  variant="outline"
                  className="w-full justify-start h-auto py-3"
                  onClick={() => handleStatusSelect(status.value as ContractStatus)}
                  disabled={isSubmitting}
                >
                  <div className="text-left">
                    <div className="font-medium">{status.label}</div>
                    <div className="text-sm text-muted-foreground">
                      {status.description}
                    </div>
                  </div>
                </Button>
              ))}
            </div>

            <div className="mt-6 flex justify-end space-x-2">
              <Button 
                variant="outline" 
                onClick={() => setOpen(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      <ConfirmDialog
        open={confirmDialog.open}
        title={`Confirm ${confirmDialog.statusInfo?.label || 'Status Update'}`}
        message={confirmDialog.statusInfo?.description || 'Are you sure you want to update the contract status?'}
        confirmText="Update Status"
        cancelText="Cancel"
        confirmVariant="default"
        onConfirm={handleConfirm}
        onCancel={() => setConfirmDialog(prev => ({ ...prev, open: false }))}
        isLoading={isSubmitting}
      />
    </>
  );
};

export default ContractStatusUpdate;
