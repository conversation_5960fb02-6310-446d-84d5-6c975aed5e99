'use client';

import { usePathname } from 'next/navigation';
import { useAuth } from '@/lib/auth/AuthContext';
import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Loading } from '@/components/ui';
import { ErrorBoundary } from '@/components/ErrorBoundary';

export default function ContractsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { loading, isInitialized } = useAuth();
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted || loading || !isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <Loading size="lg" />
      </div>
    );
  }

  const sidebarNavItems = [
    {
      title: 'Active',
      href: '/contracts?status=active',
      isActive: pathname === '/contracts' || pathname.includes('active'),
    },
    {
      title: 'Pending',
      href: '/contracts?status=pending',
      isActive: pathname.includes('pending'),
    },
    {
      title: 'Completed',
      href: '/contracts?status=completed',
      isActive: pathname.includes('completed'),
    },
    {
      title: 'Disputed',
      href: '/contracts?status=disputed',
      isActive: pathname.includes('disputed'),
    },
  ];

  return (
    <ErrorBoundary>
      <DashboardLayout
        title="Contracts"
        description="Manage your contracts and agreements"
        sidebarNavItems={sidebarNavItems}
      >
        {children}
      </DashboardLayout>
    </ErrorBoundary>
  );
}
