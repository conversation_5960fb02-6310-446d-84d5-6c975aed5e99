"use client";
import React, { createContext, useContext, useEffect, useState } from "react";
import {
  signIn as authSignIn,
  resetPassword as authResetPassword,
  confirmResetPassword,
  fetchUserAttributes,
  type SignInOutput,
  type AuthError,
} from "aws-amplify/auth";
import { Hub } from "@aws-amplify/core";
import { authService } from "./AuthService";
import * as AuthTypes from "@/types/auth";
import { getUserById } from "@/lib/user";

import { UserRole } from "@/types/enums";
type CognitoUser = AuthTypes.CognitoUser;
type SignUpParams = AuthTypes.SignUpParams;
type ProfileUpdateParams = AuthTypes.ProfileUpdateParams;
type ChallengeUser = AuthTypes.ChallengeUser;
type CustomSignInOutput = AuthTypes.CustomSignInOutput;
const { createCognitoUser } = AuthTypes;

/**
 * Extract role from JWT token groups as fallback
 * @param currentUser - the current authenticated user
 * @returns UserRole if found in groups, undefined otherwise
 */
async function getRoleFromCognitoGroups(): Promise<UserRole | undefined> {
  try {
    const { fetchAuthSession } = await import("aws-amplify/auth");
    const session = await fetchAuthSession();

    if (session.tokens?.accessToken) {
      const payload = session.tokens.accessToken.payload;
      const groups = payload["cognito:groups"] as string[] | undefined;

      if (groups && groups.length > 0) {
        for (const group of groups) {
          const groupUpper = group.toUpperCase();
          if (
            groupUpper === "CLIENT" ||
            groupUpper === "FREELANCER" ||
            groupUpper === "ADMIN"
          ) {
            return groupUpper as UserRole;
          }
        }
      }
    }
  } catch (error) {
    console.error("Error extracting role from Cognito groups:", error);
  }

  return undefined;
}

/**
 * Attempts to migrate user role from database to Cognito custom:role attribute
 * @param userIdentifier - username or email to look up in database
 * @returns UserRole if found and successfully updated, undefined otherwise
 */
async function migrateUserRoleFromDatabase(
  userIdentifier: string
): Promise<UserRole | undefined> {
  try {
    const dbUser = await getUserById(userIdentifier);

    if (dbUser && dbUser.role) {
      const roleFromDb = dbUser.role as UserRole;
      if (Object.values(UserRole).includes(roleFromDb)) {
        const { updateUserAttributes } = await import("aws-amplify/auth");
        await updateUserAttributes({
          userAttributes: {
            "custom:role": roleFromDb,
          },
        });

        console.log(
          `Successfully migrated role ${roleFromDb} from database to Cognito for user ${userIdentifier}`
        );
        return roleFromDb;
      } else {
        console.error(
          `Invalid role in database for user ${userIdentifier}:`,
          dbUser.role
        );
      }
    } else {
      console.log(
        `User ${userIdentifier} not found in database or has no role`
      );
    }
  } catch (error) {
    console.error(`Error migrating role for user ${userIdentifier}:`, error);
  }

  return undefined;
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: CognitoUser | null;
  loading: boolean;
  error: string | null;
  cognitoUserId: string | null;
  isInitialized: boolean;
  isLoggingOut: boolean;
  signIn: (email: string, password: string) => Promise<CognitoUser | void>;
  signUp: (params: SignUpParams) => Promise<AuthTypes.SignUpResult>;
  confirmSignUp: (
    username: string,
    code: string,
    password?: string
  ) => Promise<SignInOutput | boolean>;
  signOut: () => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  forgotPasswordSubmit: (
    email: string,
    code: string,
    newPassword: string
  ) => Promise<void>;
  completeNewPassword: (
    user: ChallengeUser,
    newPassword: string
  ) => Promise<SignInOutput>;
  updateProfile: (attributes: ProfileUpdateParams) => Promise<void>;
  refresh: () => Promise<CognitoUser>;
}

/**
 * The authentication context that will be used to provide auth state and methods
 * to components in the application.
 */
const AuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * AuthProvider component that wraps the application and provides authentication context.
 *
 * @param children - Child components that will have access to the auth context
 * @returns A React component that provides authentication context to its children
 */
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<CognitoUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  /**
   * Effect hook to check authentication state on mount and set up auth event listeners.
   * This runs once when the component mounts and cleans up when it unmounts.
   */
  useEffect(() => {
    checkAuthState();

    interface HubPayloadData {
      username?: string;
      attributes?: {
        email?: string;
        name?: string;
        bio?: string;
        skills?: string[];
        "custom:role"?: UserRole;
        [key: string]: unknown;
      };
      [key: string]: unknown;
    }

    interface HubPayload {
      event: string;
      data?: HubPayloadData;
    }

    const hubListener = async (data: { payload: HubPayload }) => {
      switch (data.payload.event) {
        case "signIn":
        case "autoSignIn":
          try {
            const payloadData = data.payload.data;
            if (payloadData?.username) {
              const userData: CustomSignInOutput = {
                ...payloadData,
                username: payloadData.username || "",
                attributes: {
                  email: payloadData.attributes?.email || "",
                  name: payloadData.attributes?.name || "",
                  bio: payloadData.attributes?.bio || "",
                  skills: payloadData.attributes?.skills || [],
                  "custom:role": payloadData.attributes?.[
                    "custom:role"
                  ] as UserRole,
                },
                isSignedIn: true,
                nextStep: { signInStep: "DONE" },
              };
              setUser(userData);
            } else {
              const currentUser = await authService.getCurrentUser();
              const attributes = currentUser
                ? await fetchUserAttributes()
                : ({} as any);
              const userData: CustomSignInOutput = {
                ...currentUser!,
                username: currentUser!.username || "",
                attributes: {
                  email: attributes.email || "",
                  name: attributes.name || "",
                  bio: attributes.bio || "",
                  skills: Array.isArray(attributes.skills)
                    ? attributes.skills
                    : [],
                  "custom:role": attributes["custom:role"] as UserRole,
                },
                name: attributes.name || "",
                bio: attributes.bio || "",
                skills: Array.isArray(attributes.skills)
                  ? attributes.skills
                  : [],
                isSignedIn: true,
                nextStep: { signInStep: "DONE" },
              };
              setUser(userData);
            }
            setIsAuthenticated(true);
          } catch (error) {
            console.error("Error handling auth event:", error);
            setUser(null);
            setIsAuthenticated(false);
          }
          setError(null);
          break;

        case "signOut":
          setUser(null);
          setIsAuthenticated(false);
          setError(null);
          break;

        case "signIn_failure":
          setError(
            (data.payload.data?.message as string) || "Failed to sign in"
          );
          setIsAuthenticated(false);
          setUser(null);
          break;

        case "tokenRefresh_failure":
          setError("Your session has expired. Please sign in again.");
          setIsAuthenticated(false);
          setUser(null);
          break;
      }
    };

    const hubListenerCleanup = Hub.listen(
      "auth",
      hubListener as Parameters<typeof Hub.listen>[1]
    );

    return () => {
      if (hubListenerCleanup) {
        hubListenerCleanup();
      }
    };
  }, []);

  /**
   * Checks the current authentication state by attempting to get the current user.
   * Updates the auth state in the context based on whether a user is authenticated.
   */
  const checkAuthState = async () => {
    try {
      setLoading(true);
      const currentUser = await authService.getCurrentUser();
      if (currentUser) {
        const attributes = await fetchUserAttributes();

        let userRole = attributes["custom:role"] as UserRole | undefined;

        if (!userRole) {
          console.log(
            "User role not found in Cognito attributes, attempting fallback methods..."
          );

          try {
            userRole = await getRoleFromCognitoGroups();
            if (userRole) {
              console.log(
                "Found role in Cognito groups during checkAuthState:",
                userRole
              );
              try {
                const { updateUserAttributes } = await import(
                  "aws-amplify/auth"
                );
                await updateUserAttributes({
                  userAttributes: {
                    "custom:role": userRole,
                  },
                });
                console.log(
                  "Updated custom:role attribute from Cognito groups during checkAuthState"
                );
              } catch (updateError) {
                console.error(
                  "Failed to update custom:role attribute during checkAuthState:",
                  updateError
                );
              }
            }
          } catch (groupError) {
            console.error(
              "Failed to get role from Cognito groups during checkAuthState:",
              groupError
            );
          }

          if (!userRole) {
            const userIdentifier = currentUser.username || attributes.email;
            if (userIdentifier) {
              try {
                userRole = await migrateUserRoleFromDatabase(userIdentifier);
                if (userRole) {
                  console.log(
                    "Successfully migrated role from database during checkAuthState:",
                    userRole
                  );
                }
              } catch (migrationError) {
                console.error(
                  "Failed to migrate role from database during checkAuthState:",
                  migrationError
                );
              }
            }
          }

          if (!userRole) {
            console.error(
              "User role not found in attributes, groups, or database. This should be set during registration."
            );
            console.error("User attributes:", attributes);
          }
        }

        const userData: CustomSignInOutput = {
          ...currentUser,
          username: currentUser.username || "",
          attributes: {
            email: attributes.email || "",
            name: attributes.name || "",
            "custom:role": userRole,
            sub: attributes.sub || currentUser.userId || "",
            ...(attributes as Record<string, unknown>),
          },
          isSignedIn: true,
          nextStep: { signInStep: "DONE" },
        };

        setUser(userData);
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error("Error checking auth state:", error);
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setLoading(false);
      setIsInitialized(true);
    }
  };

  /**
   * Signs in a user with the provided email and password.
   *
   * @param email - The user's email address
   * @param password - The user's password
   * @returns A Promise that resolves with the authenticated user or void if an error occurs
   * @throws {Error} If authentication fails
   */
  const signIn = async (
    email: string,
    password: string
  ): Promise<CognitoUser | void> => {
    try {
      setLoading(true);
      setError(null);
      const authUser = await authService.signIn(email, password);

      if (authUser && typeof authUser === "object" && "nextStep" in authUser) {
        if (
          authUser.nextStep.signInStep ===
          "CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED"
        ) {
          // Return a CognitoUser object that can be used to handle the new password requirement
          // Create a new user object without isSignedIn and nextStep
          // Note: Role will be determined after password change is complete
          const newUser = {
            username: email,
            attributes: {
              email: email,
              "custom:role": undefined,
            },
            challengeName: "NEW_PASSWORD_REQUIRED",
          };

          return createCognitoUser(newUser);
        }
      }

      const currentUser = await authService.getCurrentUser();

      if (!currentUser) {
        throw new Error("Failed to get current user after sign in");
      }

      const userAttributes = await fetchUserAttributes();
      let roleValue = userAttributes["custom:role"] as UserRole | undefined;

      if (
        !roleValue ||
        !Object.values(UserRole).includes(roleValue as UserRole)
      ) {
        console.log(
          "Custom role missing from Cognito, attempting fallback methods..."
        );

        try {
          roleValue = await getRoleFromCognitoGroups();
          if (roleValue) {
            console.log("Found role in Cognito groups:", roleValue);
            try {
              const { updateUserAttributes } = await import("aws-amplify/auth");
              await updateUserAttributes({
                userAttributes: {
                  "custom:role": roleValue,
                },
              });
              console.log("Updated custom:role attribute from Cognito groups");
            } catch (updateError) {
              console.error(
                "Failed to update custom:role attribute:",
                updateError
              );
            }
          }
        } catch (groupError) {
          console.error("Failed to get role from Cognito groups:", groupError);
        }

        if (!roleValue) {
          const userIdentifier = currentUser.username || userAttributes.email;
          if (userIdentifier) {
            try {
              roleValue = await migrateUserRoleFromDatabase(userIdentifier);
              if (roleValue) {
                console.log(
                  "Successfully migrated role from database:",
                  roleValue
                );
              }
            } catch (migrationError) {
              console.error(
                "Failed to migrate role from database:",
                migrationError
              );
            }
          } else {
            console.error("No user identifier available for role migration");
          }
        }
      }

      if (
        !roleValue ||
        !Object.values(UserRole).includes(roleValue as UserRole)
      ) {
        console.error(
          "Invalid or missing user role after migration attempt:",
          roleValue
        );
        console.error("User attributes:", userAttributes);

        try {
          await authService.signOut();
          setUser(null);
          setIsAuthenticated(false);
          setError(null);
        } catch (signOutError) {
          console.error(
            "Error signing out after role validation failure:",
            signOutError
          );
        }

        throw new Error(
          "User role not properly configured. Please contact support."
        );
      }

      const userRole: UserRole = roleValue;

      const cognitoUser = createCognitoUser({
        username: currentUser.username || email,
        attributes: {
          email: currentUser.signInDetails?.loginId || email,
          name:
            userAttributes.name ||
            userAttributes.given_name ||
            userAttributes.nickname ||
            "",
          "custom:role": userRole,
          sub: userAttributes.sub || currentUser.userId || "",
          ...userAttributes,
        },
      });

      setUser(cognitoUser);
      setIsAuthenticated(true);

      return cognitoUser;
    } catch (error) {
      const authError = error as AuthError;
      const errorMessage = authError?.message || "Failed to sign in";

      try {
        await authService.signOut();
      } catch (signOutError) {
        console.error(
          "Error clearing session after sign in failure:",
          signOutError
        );
      }

      setUser(null);
      setIsAuthenticated(false);
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const [signupData, setSignupData] = useState<{
    email: string;
    password: string;
  } | null>(null);

  /**
   * Signs up a new user with the provided parameters.
   *
   * @param params - Object containing signup parameters (username, password, attributes)
   * @returns A Promise that resolves with the newly created user
   * @throws {Error} If signup fails
   */
  const signUp = async (params: SignUpParams) => {
    try {
      setLoading(true);
      setError(null);

      const userRole: UserRole =
        (params.attributes["custom:role"] as UserRole) || UserRole.CLIENT;

      setSignupData({
        email: params.username,
        password: params.password,
      });

      const signUpResult = await authService.signUp(
        {
          email: params.username,
          password: params.password,
          name: params.attributes.name || "",
          role: userRole,
        },
        userRole
      );

      const cognitoUser = createCognitoUser({
        username: params.username,
        attributes: {
          email: params.attributes.email,
          name: params.attributes.name || "",
          "custom:role": userRole,
        },
      });

      return {
        user: cognitoUser,
        nextStep: {
          signUpStep: signUpResult.nextStep.signUpStep as
            | "CONFIRM_SIGN_UP"
            | "DONE",
        },
        isSignUpComplete: signUpResult.isSignUpComplete,
      };
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to sign up";
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Confirms a user's signup with the provided confirmation code.
   *
   * @param username - The username to confirm
   * @param code - The confirmation code received by the user
   * @returns A Promise that resolves with the sign-in result or true if auto-signin is not possible
   * @throws {Error} If confirmation fails
   */
  const confirmSignUp = async (
    username: string,
    code: string
  ): Promise<SignInOutput | boolean> => {
    try {
      setLoading(true);
      setError(null);

      const password = signupData?.password;

      if (!password) {
        throw new Error(
          "Password not available for verification. Please try signing up again."
        );
      }

      await authService.confirmSignUp(username, code, password);

      if (signupData) {
        const { email, password } = signupData;
        if (email && password) {
          const result = await signIn(email, password);
          if (result) {
            return result;
          }
          return true;
        }
      }

      return true;
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to confirm sign up";
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Updates the current user's profile with the provided attributes.
   *
   * @param attributes - The attributes to update
   * @returns A Promise that resolves when the update is complete
   * @throws {Error} If the update fails
   */
  const updateProfile = async (attributes: ProfileUpdateParams) => {
    try {
      setLoading(true);
      setError(null);

      if (user) {
        const updatedUser = {
          ...user,
          attributes: {
            ...user.attributes,
            ...attributes,
          },
        };
        setUser(updatedUser);
      }

    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update profile";
      console.error("Error updating profile:", error);
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const refresh = async () => {
    try {
      const currentUser = await authService.getCurrentUser();
      const attributes = currentUser
        ? await fetchUserAttributes()
        : ({} as any);
      const userData: CustomSignInOutput = {
        ...currentUser!,
        username: currentUser!.username || "",
        attributes: {
          email: attributes.email || "",
          name: attributes.name || "",
          "custom:role": attributes["custom:role"] as UserRole,
        },
        isSignedIn: true,
        nextStep: { signInStep: "DONE" },
      };
      setUser(userData);
      return userData;
    } catch (error) {
      console.error("Error refreshing user data:", error);
      setUser(null);
      setIsAuthenticated(false);
      throw error;
    }
  };

  const contextValue: AuthContextType = {
    isAuthenticated,
    user,
    loading,
    error,
    cognitoUserId: user?.attributes?.sub || null,
    isInitialized,
    isLoggingOut,
    signIn,
    signUp,
    confirmSignUp,
    updateProfile,
    signOut: async () => {
      try {
        setIsLoggingOut(true);
        setLoading(true);
        await authService.signOut();
        setUser(null);
        setIsAuthenticated(false);
        setError(null);

        if (typeof window !== "undefined") {
          window.location.href = "/login";
        }
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to sign out";
        setError(errorMessage);
        setIsLoggingOut(false);
        throw new Error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
    forgotPassword: async (email: string) => {
      try {
        setLoading(true);
        await authResetPassword({ username: email });
        setError(null);
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to reset password";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
    forgotPasswordSubmit: async (
      email: string,
      code: string,
      newPassword: string
    ) => {
      try {
        setLoading(true);
        await confirmResetPassword({
          username: email,
          confirmationCode: code,
          newPassword,
        });
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to reset password";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
    refresh,
    completeNewPassword: async (
      challengeUser: ChallengeUser,
      newPassword: string
    ) => {
      try {
        setLoading(true);

        const username = challengeUser.username;
        if (!username) {
          throw new Error("Username is required");
        }

        const signInResult = await authSignIn({
          username,
          password: newPassword,
        });

        if (!signInResult.isSignedIn) {
          throw new Error("Failed to sign in with new password");
        }

        const attributes = await fetchUserAttributes();

        const userData: CustomSignInOutput = {
          ...signInResult,
          username,
          attributes: {
            email: attributes.email || "",
            name: attributes.name || "",
            bio: attributes.bio || "",
            skills: Array.isArray(attributes.skills) ? attributes.skills : [],
            "custom:role": attributes["custom:role"] as UserRole,
          },
          name: attributes.name || "",
          bio: attributes.bio || "",
          skills: Array.isArray(attributes.skills) ? attributes.skills : [],
          isSignedIn: true,
          nextStep: { signInStep: "DONE" },
        };

        setUser(userData);
        setIsAuthenticated(true);

        return signInResult as SignInOutput;
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to complete new password";
        setError(errorMessage);
        throw new Error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

/**
 * Custom hook that provides access to the authentication context.
 * Must be used within an AuthProvider.
 *
 * @returns The authentication context value
 * @throws {Error} If used outside of an AuthProvider
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
