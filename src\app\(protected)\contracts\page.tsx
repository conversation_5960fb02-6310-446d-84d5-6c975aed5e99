'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState, useCallback } from 'react';
import Link from 'next/link';

import { useAuth } from '@/lib/auth/AuthContext';
import { Tabs, type TabItem } from '@/components/ui/Tabs';
import { Button } from '@/components/ui/Button';
import { Loading } from '@/components/ui';
import { useToast } from '@/components/ui/toast';
import contractService from '@/api/contracts/contract.service';
import { Contract, ContractStatus } from '@/types/features/contracts/contract.types';

const statusTabs = [
  {
    value: 'active',
    label: 'Active',
    status: ContractStatus.ACTIVE,
    description: 'View and manage your active contracts',
  },
  {
    value: 'pending',
    label: 'Pending',
    status: ContractStatus.DRAFT,
    description: 'Review and respond to pending contract offers',
  },
  {
    value: 'completed',
    label: 'Completed',
    status: ContractStatus.COMPLETED,
    description: 'View your completed contracts and history',
  },
  {
    value: 'disputed',
    label: 'Disputed',
    status: ContractStatus.DISPUTED,
    description: 'Resolve any contract disputes',
  },
  {
    value: 'cancelled',
    label: 'Cancelled',
    status: ContractStatus.CANCELLED,
    description: 'View cancelled contracts',
  },
];

export default function ContractsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, loading: authLoading, isAuthenticated } = useAuth();
  const { showToast } = useToast();
  
  const [activeTab, setActiveTab] = useState('active');
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);
  const [isFreelancer, setIsFreelancer] = useState(false);

  // Fetch contracts based on active tab and user role
  const fetchContracts = useCallback(async () => {
    if (!user) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const status = statusTabs.find(tab => tab.value === activeTab)?.status;
      const filters: any = {
        status,
      };
      
      if (isClient) {
        filters.clientId = user.attributes.sub;
      } else if (isFreelancer) {
        filters.freelancerId = user.attributes.sub;
      }
      
      const { items } = await contractService.listContracts(filters);
      setContracts(items || []);
    } catch (err) {
      console.error('Error fetching contracts:', err);
      setError('Failed to load contracts. Please try again.');
      showToast('Failed to load contracts. Please try again.');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [activeTab, isClient, isFreelancer, user, showToast]);

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchContracts();
  };

  // Set active tab from URL
  useEffect(() => {
    const status = searchParams.get('status');
    if (status && ['active', 'pending', 'completed', 'disputed', 'cancelled'].includes(status)) {
      setActiveTab(status);
    } else {
      router.replace('/contracts?status=active');
    }
  }, [searchParams, router]);

  // Set user role
  useEffect(() => {
    if (user) {
      const userRole = user.attributes?.['custom:role'];
      setIsClient(userRole === 'CLIENT');
      setIsFreelancer(userRole === 'FREELANCER');
    }
  }, [user]);

  // Fetch contracts when active tab or user changes
  useEffect(() => {
    if (user) {
      fetchContracts();
    }
  }, [user, activeTab, fetchContracts]);

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [authLoading, isAuthenticated, router]);

  if (authLoading || !user) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loading size="lg" />
      </div>
    );
  }

  // Prepare tab items for the Tabs component
  const tabItems: TabItem[] = statusTabs.map(tab => ({
    id: tab.value,
    label: tab.label,
    content: (
      <div className="bg-card rounded-lg border mt-4">
        {error ? (
          <div className="p-6 text-center">
            <p className="text-destructive mb-4">{error}</p>
            <Button 
              variant="outline" 
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              {isRefreshing ? 'Retrying...' : 'Retry'}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-24 bg-muted/30 rounded-lg animate-pulse" />
                ))}
              </div>
            ) : contracts.length > 0 ? (
              <div className="space-y-4">
                {contracts.map((contract) => (
                  <div 
                    key={contract.id}
                    className="p-4 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
                    onClick={() => {
                      router.push(`/contracts/${contract.id}`);
                    }}
                  >
                    <h3 className="font-medium">{contract.title || `Contract #${contract.contractNumber || contract.id.substring(0, 8)}`}</h3>
                    <p className="text-sm text-muted-foreground">
                      {contract.status} • {new Date(contract.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No contracts found</p>
                <Button 
                  variant="outline" 
                  className="mt-4"
                  onClick={handleRefresh}
                >
                  Refresh
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    )
  }));

  // Get current tab description
  const currentTab = statusTabs.find(tab => tab.value === activeTab);

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Contracts</h1>
            <p className="text-muted-foreground">
              {currentTab?.description || 'Manage your contracts'}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              {isRefreshing ? 'Refreshing...' : 'Refresh'}
            </Button>
            
            {isClient && (
              <Button asChild>
                <Link href="/contracts/new">
                  New Contract
                </Link>
              </Button>
            )}
          </div>
        </div>
      </div>

      <Tabs 
        items={tabItems}
        defaultTab={activeTab}
        onChange={(tabId) => {
          setActiveTab(tabId);
          router.push(`/contracts?status=${tabId}`);
        }}
        variant="pills"
        className="mt-4"
      />
    
    </div>
  );
}
